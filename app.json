{"expo": {"name": "<PERSON><PERSON>", "slug": "mizan-banking-app", "version": "1.0.0", "web": {"favicon": "./assets/favicon.png"}, "experiments": {"tsconfigPaths": true}, "plugins": [["expo-font", {"fonts": ["node_modules/@expo-google-fonts/poppins/900Black/Poppins_900Black.ttf"]}]], "orientation": "portrait", "icon": "./assets/gradient-logo.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/LauncherIcon.png", "resizeMode": "contain", "backgroundColor": "#FFFFFF"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.wingi.mizanbankingapp", "buildNumber": "1", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/LauncherIcon.png", "backgroundColor": "#ffffff"}, "package": "com.wingi.mizanbankingapp"}, "extra": {"eas": {"projectId": "2840f4e8-0d73-4221-a407-eceb34e28db3"}}}}